import { Component, Input } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ManagedAccountService } from '../../managed-account.service';
import { ToastrService } from 'ngx-toastr';
import { ManagedAccountPermissionConstants, TOASTER_MSG } from 'src/app/common/constants';
import { ManagedAccount } from '../../managed-account.model';
import { INavigationTabLink } from '../../shared/navigationLink.model';

@Component({
  selector: 'app-investment-page',
  templateUrl: './investment-page.component.html',
  styleUrls: ['./investment-page.component.scss']
})
export class InvestmentPageComponent {
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean } };
  managedAccountId: string;
  isLoading: boolean = true;
  managedAccountData: ManagedAccount;
  public ManagedAccountPermissionConstants = ManagedAccountPermissionConstants;

  public navigationTabLinks :INavigationTabLink[]=[
    { name: 'Performance',aliasName: 'Performance',tableName: 'Performance', moduleId:58 ,isSelected: false},
    { name: 'NAVData',aliasName: 'NAV Data',tableName: 'NAV Data',isSelected: false},
    { name: 'Income_Distributions',aliasName: 'Income Distributions',tableName: 'Income Distributions (Investment Page)',isSelected: false},
  ];
  
  constructor(private route: ActivatedRoute,
    private managedAccountService: ManagedAccountService,
    private toastrService: ToastrService,
    private router: Router) { }

  ngOnInit() {
    this.isLoading = false;
    this.route.paramMap.subscribe(params => {
      this.managedAccountId = params.get('id');
      this.loadInvestmentPage(this.managedAccountId);
    });
  }

  loadInvestmentPage(managedAccountId: string) {
    this.isLoading = true;
    this.managedAccountService.getManagedAccountById(managedAccountId).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data == null) {
          this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
          this.router.navigate(['/managed-accounts']);
          return;
        }
        this.managedAccountData = data;
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error(TOASTER_MSG.NOT_FOUND, "", { positionClass: TOASTER_MSG.POS_CENTER });
        this.router.navigate(['/managed-accounts']);
      }
    });
  }
  

  redirectToEditPage(step: number) {
    this.router.navigate(['/add-managed-account', this.managedAccountId], { queryParams: { step: step } });
  }

}

// In your template (investment-page.component.html), use:
// *ngFor="let tab of navigationTabLinks"
// *ngIf="canViewTab(tab)"   <!-- Only show tabs the user can view -->

