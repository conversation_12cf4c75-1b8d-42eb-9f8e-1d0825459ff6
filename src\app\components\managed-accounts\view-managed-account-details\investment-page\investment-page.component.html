<div class="company-facts-invest-summary">
    <div style="padding-bottom: 20px;">
        <ng-container>
            <!-- Managed Account Facts Section -->
            <ng-container *ngIf="permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountFacts]?.canView">
                <div class="company-facts-staticDataContainer">
                    <div class="company-facts">
                        <span class="company-facts-section">Managed Account Facts</span>
                        <span *ngIf="permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.ManagedAccountFacts]?.canEdit" class="edit-icon" id="edit-account-summary" (click)="redirectToEditPage()"
                            (keypress)="redirectToEditPage(1)">
                            <img alt="" src="assets/dist/images/clo_edit.svg" />
                        </span>
                    </div>
                    <div class="container-fluid p-4 custom-border">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-6">
                                <div class="card h-100 border-0">
                                    <div class="card-body">
                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Account Name</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.managedAccountName?.length > 35 ? managedAccountData.managedAccountName : ''">
                                                {{managedAccountData?.managedAccountName}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Domicile</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.domicile?.length > 35 ? managedAccountData.domicile : ''">
                                                {{managedAccountData?.domicile}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Commencement Date</div>
                                            <div class="col-8 text-truncate">{{managedAccountData?.commencementDate |
                                                date:'shortDate'}}</div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Investment Period End</div>
                                            <div class="col-8 text-truncate">{{managedAccountData?.investmentPeriodEndDate}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Maturity Date</div>
                                            <div class="col-8 text-truncate">{{managedAccountData?.maturityDate}}</div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Commitment Outstanding</div>
                                            <div class="col-8 text-truncate">
                                                {{managedAccountData?.commitmentOutstanding}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <div class="card h-100 border-0">
                                    <div class="card-body">

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Base Currency</div>
                                            <div class="col-8 text-truncate">
                                                {{managedAccountData?.baseCurrency}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Investment Manager</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.investmentManager?.length > 35 ? managedAccountData.investmentManager : ''">
                                                {{managedAccountData?.investmentManager}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Administrator</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.administrator?.length > 35 ? managedAccountData.administrator : ''">
                                                {{managedAccountData?.administrator}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Custodian</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.custodian?.length > 35 ? managedAccountData.custodian : ''">
                                                {{managedAccountData?.custodian}}
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">Legal Counsel</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.legalCounsel?.length > 35 ? managedAccountData.legalCounsel : ''">
                                                {{managedAccountData?.legalCounsel}}
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="col-4 text-secondary">LEI</div>
                                            <div class="col-8 text-truncate"
                                                [title]="managedAccountData?.lei?.length > 35 ? managedAccountData.lei : ''">
                                                {{managedAccountData?.lei}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
            <!-- Investment Summary Section -->
            <ng-container *ngIf="permissions?.[ManagedAccountPermissionConstants.ManagedAccountSubFeature.InvestmentSummary]?.canView">
                <div class="company-facts-staticDataContainer investment-summary-no-margin">
                    <div class="company-facts">
                        <span class="company-facts-section">Investment Summary</span>
                        <span *ngIf="permissions?.['Investment Summary']?.canEdit" id="edit-investment-summary" class="edit-icon" (click)="redirectToEditPage(2)"
                            (keypress)="redirectToEditPage(2)"><img alt="" src="assets/dist/images/clo_edit.svg" /></span>
                    </div>
                    <div class="investment-summary">
                        {{managedAccountData?.investmentSummary}}
                    </div>
                </div>
            </ng-container>
            <!-- Navigation Tabs -->
            <div *ngFor="let navigationTab of navigationTabLinks;index as i">
                <ng-container *ngIf="permissions?.[navigationTab.tableName]?.canView">
                    <div class="company-facts-staticDataContainer investment-summary-no-borders">
                        <div class="company-facts-glo-container">
                            <app-managed-account-data-table 
                                [tableTitle]='navigationTab.tableName' 
                                [data]=''
                                [moduleId]="navigationTab.moduleId"
                                [companyID]="managedAccountId"
                                [tableName]='navigationTab.tableName'
                                [permissions]="permissions">
                            </app-managed-account-data-table>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container>
    </div>
</div>